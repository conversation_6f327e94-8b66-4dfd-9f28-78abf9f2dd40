"""Jinja2 template engine for configuration rendering."""

import asyncio
from pathlib import Path
from typing import Dict, Any, List, Optional
from jinja2 import Environment, FileSystemLoader, TemplateNotFound, TemplateError
import structlog

logger = structlog.get_logger(__name__)


class TemplateEngine:
    """Jinja2-based template engine for rendering Alloy configurations."""
    
    def __init__(self, template_folder: str) -> None:
        """Initialize template engine.
        
        Args:
            template_folder: Path to folder containing .j2 template files
        """
        self.template_folder = Path(template_folder)
        self.env = Environment(
            loader=FileSystemLoader(str(self.template_folder)),
            enable_async=True,
            trim_blocks=True,
            lstrip_blocks=True,
        )
        self._templates: Dict[str, str] = {}
    
    async def load_templates(self) -> None:
        """Load and validate all .j2 templates from the template folder."""
        if not self.template_folder.exists():
            raise FileNotFoundError(f"Template folder not found: {self.template_folder}")
        
        template_files = list(self.template_folder.glob("*.conf.j2"))
        
        if not template_files:
            logger.warning(
                "No template files found",
                folder=str(self.template_folder),
                pattern="*.conf.j2"
            )
            return
        
        self._templates.clear()
        
        for template_file in template_files:
            template_name = template_file.stem.removesuffix(".conf")
            
            try:
                # Test template compilation
                template = self.env.get_template(template_file.name)
                
                # Test rendering with dummy data
                await template.render_async(Id="test", Attributes={})
                
                self._templates[template_name] = template_file.name
                logger.info(
                    "Loaded template",
                    name=template_name,
                    file=template_file.name
                )
                
            except TemplateError as e:
                logger.error(
                    "Failed to load template",
                    name=template_name,
                    file=template_file.name,
                    error=str(e)
                )
                raise
        
        # Ensure default template exists
        if "default" not in self._templates:
            raise ValueError("Default template (default.conf.j2) is required")
        
        logger.info(
            "Template loading complete",
            count=len(self._templates),
            templates=list(self._templates.keys())
        )
    
    async def render(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render a template with the given context.
        
        Args:
            template_name: Name of template to render (without .conf.j2 suffix)
            context: Template context variables
            
        Returns:
            Rendered configuration content
            
        Raises:
            TemplateNotFound: If template doesn't exist and no default fallback
            TemplateError: If template rendering fails
        """
        # Try requested template first
        template_file = self._templates.get(template_name)
        
        if template_file is None:
            logger.warning(
                "Template not found, falling back to default",
                requested=template_name,
                available=list(self._templates.keys())
            )
            template_file = self._templates.get("default")
            
            if template_file is None:
                raise TemplateNotFound(f"Template '{template_name}' not found and no default template available")
        
        try:
            template = self.env.get_template(template_file)
            content = await template.render_async(**context)
            
            logger.debug(
                "Template rendered successfully",
                template=template_name,
                context_keys=list(context.keys())
            )
            
            return content
            
        except TemplateError as e:
            logger.error(
                "Template rendering failed",
                template=template_name,
                error=str(e),
                context=context
            )
            raise
    
    def list_templates(self) -> List[str]:
        """Get list of available template names.
        
        Returns:
            List of template names (without .conf.j2 suffix)
        """
        return list(self._templates.keys())
    
    def has_template(self, template_name: str) -> bool:
        """Check if a template exists.
        
        Args:
            template_name: Template name to check
            
        Returns:
            True if template exists, False otherwise
        """
        return template_name in self._templates
