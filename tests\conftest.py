"""Pytest configuration and fixtures."""

import asyncio
import tempfile
from pathlib import Path
from typing import AsyncGenerator
import pytest
import pytest_asyncio

from alloy_config_server.config import Settings
from alloy_config_server.storage import MemoryStorage
from alloy_config_server.templates import TemplateEngine


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_settings() -> Settings:
    """Create test settings."""
    return Settings(
        grpc_port=18888,
        http_port=18080,
        config_folder="tests/fixtures/templates",
        use_redis=False,
        org_name="test",
        log_level="DEBUG",
        debug=True
    )


@pytest.fixture
async def memory_storage() -> AsyncGenerator[MemoryStorage, None]:
    """Create memory storage for testing."""
    storage = MemoryStorage(org_name="test")
    yield storage
    await storage.close()


@pytest.fixture
def temp_template_dir():
    """Create temporary directory with test templates."""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create default template
        default_template = temp_path / "default.conf.j2"
        default_template.write_text("""
// Test template for {{ Id }}
prometheus.exporter.unix "default" {
    // Attributes: {{ Attributes }}
}
""".strip())
        
        # Create custom template
        custom_template = temp_path / "custom.conf.j2"
        custom_template.write_text("""
// Custom template for {{ Id }}
{% for key, value in Attributes.items() %}
// {{ key }}: {{ value }}
{% endfor %}
loki.echo "custom" {
}
""".strip())
        
        yield str(temp_path)


@pytest.fixture
async def template_engine(temp_template_dir) -> AsyncGenerator[TemplateEngine, None]:
    """Create template engine with test templates."""
    engine = TemplateEngine(temp_template_dir)
    await engine.load_templates()
    yield engine
