# Python Refactor Plan: Alloy Remote Config Server

## Overview
This document outlines the comprehensive plan to refactor the Go-based alloy-remote-config-server to Python, maintaining full compatibility with Grafana Alloy agents while leveraging Python's strengths.

## Current Go Implementation Analysis

### Architecture Components
1. **Main Entry Point** (`cmd/config/main.go`) - Simple startup
2. **gRPC Service** (`pkg/config/grpc.go`) - Connect-style gRPC with Alloy protocol
3. **REST API** (`pkg/config/rest.go`) - Gin-based HTTP endpoints
4. **Template Engine** (`pkg/config/template.go`) - Go template processing
5. **Storage Layer** (`pkg/config/storage.go`) - Memory/Redis abstraction
6. **Server Coordination** (`pkg/config/server.go`) - Environment config & startup

### Key Dependencies
- `connectrpc.com/connect` - gRPC Connect protocol
- `github.com/gin-gonic/gin` - HTTP web framework
- `github.com/go-redis/redis/v8` - Redis client
- `github.com/grafana/alloy-remote-config` - Alloy protocol definitions
- `html/template` - Go templating engine

### Core Functionality
- Serves gRPC `GetConfig` requests from Alloy agents
- Processes agent metadata (ID, attributes) through Go templates
- Caches resolved configurations in memory or Redis
- Provides REST endpoints for debugging and management
- Supports multi-tenancy via organization namespacing

## Python Architecture Design

### Technology Stack
- **gRPC Framework**: `grpcio` + `grpcio-tools` for protocol buffers
- **Web Framework**: `FastAPI` (async, modern, auto-docs)
- **Template Engine**: `Jinja2` (more powerful than Go templates)
- **Redis Client**: `redis-py` with async support
- **Configuration**: `pydantic-settings` for type-safe env vars
- **Async Runtime**: `asyncio` for concurrent servers
- **Testing**: `pytest` + `pytest-asyncio`
- **Packaging**: `poetry` for dependency management

### Project Structure
```
alloy-remote-config-server-py/
├── pyproject.toml              # Poetry configuration
├── README.md                   # Python-specific documentation
├── Dockerfile                  # Python container
├── docker-compose.yml          # Development environment
├── .env.example               # Environment variables template
├── src/
│   └── alloy_config_server/
│       ├── __init__.py
│       ├── main.py            # Application entry point
│       ├── config/
│       │   ├── __init__.py
│       │   └── settings.py    # Pydantic settings
│       ├── grpc/
│       │   ├── __init__.py
│       │   ├── server.py      # gRPC service implementation
│       │   └── proto/         # Generated protobuf files
│       ├── rest/
│       │   ├── __init__.py
│       │   └── api.py         # FastAPI REST endpoints
│       ├── storage/
│       │   ├── __init__.py
│       │   ├── base.py        # Storage interface
│       │   ├── memory.py      # In-memory storage
│       │   └── redis.py       # Redis storage
│       ├── templates/
│       │   ├── __init__.py
│       │   └── engine.py      # Jinja2 template processing
│       └── utils/
│           ├── __init__.py
│           └── logging.py     # Structured logging
├── tests/
│   ├── __init__.py
│   ├── conftest.py           # Pytest configuration
│   ├── test_grpc.py          # gRPC service tests
│   ├── test_rest.py          # REST API tests
│   ├── test_storage.py       # Storage layer tests
│   └── test_templates.py     # Template engine tests
└── conf/
    └── default.conf.j2       # Jinja2 template (converted from .tmpl)
```

## Implementation Phases

### Phase 1: Project Foundation
1. **Setup Python project structure** with Poetry
2. **Configure development environment** (Docker, pre-commit hooks)
3. **Generate Python gRPC stubs** from Alloy protobuf definitions
4. **Create base configuration** with Pydantic settings

### Phase 2: Core Components
1. **Storage Layer** - Abstract interface with memory/Redis implementations
2. **Template Engine** - Jinja2-based template processing
3. **gRPC Service** - Implement Alloy protocol handlers
4. **REST API** - FastAPI endpoints for management

### Phase 3: Integration & Testing
1. **Server coordination** - Async startup of both gRPC and HTTP servers
2. **Configuration management** - Environment variable handling
3. **Comprehensive testing** - Unit and integration tests
4. **Docker containerization** - Multi-stage Python container

### Phase 4: Validation & Documentation
1. **Compatibility testing** with real Alloy agents
2. **Performance benchmarking** vs Go implementation
3. **Documentation updates** for Python-specific features
4. **Migration guide** for existing deployments

## Key Translation Challenges & Solutions

### 1. gRPC Protocol Compatibility
**Challenge**: Ensure Python gRPC service is compatible with Alloy agents
**Solution**: Use official protobuf definitions and test with real agents

### 2. Template Syntax Migration
**Challenge**: Convert Go templates to Jinja2 syntax
**Solution**: Create migration utility and document syntax differences

### 3. Concurrent Server Management
**Challenge**: Running gRPC and HTTP servers concurrently
**Solution**: Use asyncio.gather() to run both servers in event loop

### 4. Redis Connection Management
**Challenge**: Proper async Redis connection handling
**Solution**: Use connection pooling with proper lifecycle management

## Benefits of Python Implementation

1. **Better Async Support** - Native asyncio for handling concurrent requests
2. **Superior Templating** - Jinja2 offers more features than Go templates
3. **Rich Ecosystem** - Extensive Python libraries for observability
4. **Type Safety** - Pydantic for runtime type validation
5. **Testing Framework** - pytest provides excellent testing capabilities
6. **Team Familiarity** - Leverages team's Python expertise

## Migration Strategy

1. **Parallel Development** - Build Python version alongside Go version
2. **Feature Parity** - Ensure 100% functional compatibility
3. **Gradual Rollout** - Test with subset of Alloy agents first
4. **Monitoring** - Compare performance and reliability metrics
5. **Documentation** - Provide clear migration path for operators

## Success Criteria

- [ ] Python service passes all existing Go service tests
- [ ] Compatible with Grafana Alloy agents (tested with real agents)
- [ ] Performance within 10% of Go implementation
- [ ] Complete test coverage (>90%)
- [ ] Docker container size comparable to Go version
- [ ] Clear documentation and migration guide
