"""FastAPI REST API implementation."""

from typing import List
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.responses import PlainTextResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import structlog

from ..storage.base import StorageInterface
from ..templates.engine import Template<PERSON>ngine
from ..config.settings import Settings

logger = structlog.get_logger(__name__)


def create_app(
    storage: StorageInterface, 
    template_engine: TemplateEngine,
    settings: Settings
) -> FastAPI:
    """Create FastAPI application.
    
    Args:
        storage: Storage backend for configurations
        template_engine: Template engine for rendering
        settings: Application settings
        
    Returns:
        Configured FastAPI application
    """
    app = FastAPI(
        title="Alloy Remote Config Server",
        description="Python implementation of Grafana Alloy Remote Configuration Server",
        version="0.1.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
    )
    
    # Add CORS middleware for development
    if settings.debug:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        # Check storage health if it's Redis
        storage_healthy = True
        if hasattr(storage, 'health_check'):
            storage_healthy = await storage.health_check()
        
        status = "healthy" if storage_healthy else "unhealthy"
        status_code = 200 if storage_healthy else 503
        
        return JSONResponse(
            content={
                "status": status,
                "storage": "healthy" if storage_healthy else "unhealthy",
                "templates": len(template_engine.list_templates())
            },
            status_code=status_code
        )
    
    # Template management endpoints
    @app.get("/templates", response_model=List[str])
    async def list_templates():
        """List available configuration templates.
        
        Returns:
            List of template names
        """
        try:
            templates = template_engine.list_templates()
            logger.debug("Templates listed", count=len(templates))
            return templates
        except Exception as e:
            logger.error("Failed to list templates", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to list templates")
    
    # Configuration management endpoints
    @app.get("/configs", response_model=List[str])
    async def list_configs():
        """List all cached configuration IDs.
        
        Returns:
            List of configuration identifiers
        """
        try:
            configs = await storage.get_all()
            logger.debug("Configs listed", count=len(configs))
            return configs
        except Exception as e:
            logger.error("Failed to list configs", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to list configurations")
    
    @app.get("/configs/{config_id}", response_class=PlainTextResponse)
    async def get_config(config_id: str):
        """Get a specific cached configuration.
        
        Args:
            config_id: Configuration identifier
            
        Returns:
            Configuration content as plain text
        """
        try:
            config = await storage.get(config_id)
            if config is None:
                logger.warning("Config not found", config_id=config_id)
                raise HTTPException(status_code=404, detail="Configuration not found")
            
            logger.debug("Config retrieved", config_id=config_id, length=len(config))
            return config
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error("Failed to get config", config_id=config_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to retrieve configuration")
    
    @app.delete("/configs/{config_id}")
    async def delete_config(config_id: str):
        """Delete a cached configuration.
        
        Args:
            config_id: Configuration identifier
            
        Returns:
            Success message
        """
        # Note: This endpoint is not in the original Go implementation
        # but could be useful for cache management
        try:
            config = await storage.get(config_id)
            if config is None:
                raise HTTPException(status_code=404, detail="Configuration not found")
            
            # For memory storage, we'd need to implement a delete method
            # For Redis, we could use DEL command
            # This is a placeholder for now
            logger.info("Config deletion requested", config_id=config_id)
            return {"message": f"Configuration {config_id} deletion requested"}
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error("Failed to delete config", config_id=config_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to delete configuration")
    
    # Template rendering endpoint (for testing)
    @app.post("/render/{template_name}", response_class=PlainTextResponse)
    async def render_template(
        template_name: str,
        context: dict
    ):
        """Render a template with provided context (for testing).
        
        Args:
            template_name: Name of template to render
            context: Template context variables
            
        Returns:
            Rendered configuration content
        """
        if not settings.debug:
            raise HTTPException(status_code=404, detail="Not found")
        
        try:
            content = await template_engine.render(template_name, context)
            logger.debug(
                "Template rendered via API",
                template=template_name,
                context_keys=list(context.keys())
            )
            return content
            
        except Exception as e:
            logger.error(
                "Template rendering failed via API",
                template=template_name,
                error=str(e)
            )
            raise HTTPException(
                status_code=400, 
                detail=f"Template rendering failed: {str(e)}"
            )
    
    # Add startup event
    @app.on_event("startup")
    async def startup_event():
        """Application startup event."""
        logger.info("REST API starting up")
        
        # Load templates
        try:
            await template_engine.load_templates()
            logger.info("Templates loaded successfully")
        except Exception as e:
            logger.error("Failed to load templates", error=str(e))
            raise
    
    # Add shutdown event
    @app.on_event("shutdown")
    async def shutdown_event():
        """Application shutdown event."""
        logger.info("REST API shutting down")
        
        # Close storage connections
        try:
            await storage.close()
            logger.info("Storage connections closed")
        except Exception as e:
            logger.warning("Error closing storage", error=str(e))
    
    return app
