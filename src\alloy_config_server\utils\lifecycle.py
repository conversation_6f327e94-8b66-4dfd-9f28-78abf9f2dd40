"""Application lifecycle management."""

import asyncio
import signal
from typing import List, Callable, Awaitable
import structlog

logger = structlog.get_logger(__name__)


class LifecycleManager:
    """Manages application lifecycle and graceful shutdown."""
    
    def __init__(self) -> None:
        """Initialize lifecycle manager."""
        self.shutdown_handlers: List[Callable[[], Awaitable[None]]] = []
        self.startup_handlers: List[Callable[[], Awaitable[None]]] = []
        self._shutdown_event = asyncio.Event()
    
    def add_startup_handler(self, handler: Callable[[], Awaitable[None]]) -> None:
        """Add a startup handler.
        
        Args:
            handler: Async function to call during startup
        """
        self.startup_handlers.append(handler)
    
    def add_shutdown_handler(self, handler: Callable[[], Awaitable[None]]) -> None:
        """Add a shutdown handler.
        
        Args:
            handler: Async function to call during shutdown
        """
        self.shutdown_handlers.append(handler)
    
    def setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info("Received shutdown signal", signal=signum)
            self._shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def startup(self) -> None:
        """Run all startup handlers."""
        logger.info("Running startup handlers", count=len(self.startup_handlers))
        
        for i, handler in enumerate(self.startup_handlers):
            try:
                await handler()
                logger.debug("Startup handler completed", index=i)
            except Exception as e:
                logger.error("Startup handler failed", index=i, error=str(e))
                raise
        
        logger.info("All startup handlers completed")
    
    async def shutdown(self) -> None:
        """Run all shutdown handlers."""
        logger.info("Running shutdown handlers", count=len(self.shutdown_handlers))
        
        # Run shutdown handlers in reverse order
        for i, handler in enumerate(reversed(self.shutdown_handlers)):
            try:
                await handler()
                logger.debug("Shutdown handler completed", index=i)
            except Exception as e:
                logger.warning("Shutdown handler failed", index=i, error=str(e))
                # Continue with other handlers even if one fails
        
        logger.info("All shutdown handlers completed")
    
    async def wait_for_shutdown(self) -> None:
        """Wait for shutdown signal."""
        await self._shutdown_event.wait()
    
    def trigger_shutdown(self) -> None:
        """Manually trigger shutdown."""
        self._shutdown_event.set()
