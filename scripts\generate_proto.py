#!/usr/bin/env python3
"""Script to generate Python protobuf files from .proto definitions."""

import subprocess
import sys
from pathlib import Path


def main():
    """Generate protobuf files."""
    # Get project root
    project_root = Path(__file__).parent.parent
    proto_dir = project_root / "proto"
    output_dir = project_root / "src" / "alloy_config_server" / "grpc" / "proto"
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create __init__.py files
    (output_dir / "__init__.py").touch()
    
    # Find all .proto files
    proto_files = list(proto_dir.rglob("*.proto"))
    
    if not proto_files:
        print("No .proto files found!")
        return 1
    
    print(f"Found {len(proto_files)} proto files:")
    for proto_file in proto_files:
        print(f"  {proto_file}")
    
    # Generate Python files
    cmd = [
        sys.executable, "-m", "grpc_tools.protoc",
        f"--proto_path={proto_dir}",
        f"--python_out={output_dir}",
        f"--grpc_python_out={output_dir}",
        f"--pyi_out={output_dir}",  # Generate type stubs
    ]
    
    # Add all proto files
    for proto_file in proto_files:
        cmd.append(str(proto_file.relative_to(proto_dir)))
    
    print(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Protobuf generation successful!")
        if result.stdout:
            print("STDOUT:", result.stdout)
        return 0
    except subprocess.CalledProcessError as e:
        print("❌ Protobuf generation failed!")
        print("STDERR:", e.stderr)
        print("STDOUT:", e.stdout)
        return 1


if __name__ == "__main__":
    sys.exit(main())
