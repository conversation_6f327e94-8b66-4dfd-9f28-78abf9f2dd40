# Python Architecture Design

## Overview
This document details the Python architecture design for the alloy-remote-config-server refactor, ensuring full compatibility with Grafana Alloy agents while leveraging Python's strengths.

## Protobuf Analysis

Based on the official Grafana Alloy Remote Config protobuf definition:

```protobuf
service CollectorService {
  rpc GetConfig (GetConfigRequest) returns (GetConfigResponse);
  rpc RegisterCollector (RegisterCollectorRequest) returns (RegisterCollectorResponse);
  rpc UnregisterCollector (UnregisterCollectorRequest) returns (UnregisterCollectorResponse);
}

message GetConfigRequest {
  string id = 1;
  map<string, string> attributes = 2 [deprecated=true];
  map<string, string> local_attributes = 4;
  string hash = 3;
}

message GetConfigResponse {
  string content = 1;
  string hash = 2;
  bool not_modified = 3;
}
```

## Python Technology Stack

### Core Dependencies
```toml
[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.0"           # Modern async web framework
uvicorn = "^0.24.0"            # ASGI server
grpcio = "^1.59.0"             # gRPC runtime
grpcio-tools = "^1.59.0"       # Protocol buffer compiler
jinja2 = "^3.1.2"              # Template engine
redis = "^5.0.0"               # Redis client with async support
pydantic = "^2.5.0"            # Data validation
pydantic-settings = "^2.1.0"   # Settings management
structlog = "^23.2.0"          # Structured logging
```

### Development Dependencies
```toml
[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-grpc = "^0.8.0"
black = "^23.0.0"
ruff = "^0.1.0"
mypy = "^1.7.0"
pre-commit = "^3.5.0"
```

## Project Structure

```
alloy-remote-config-server-py/
├── pyproject.toml                    # Poetry configuration
├── README.md                         # Python-specific docs
├── Dockerfile                        # Multi-stage Python container
├── docker-compose.yml                # Development environment
├── .env.example                      # Environment template
├── .pre-commit-config.yaml           # Code quality hooks
├── src/
│   └── alloy_config_server/
│       ├── __init__.py
│       ├── main.py                   # Application entry point
│       ├── config/
│       │   ├── __init__.py
│       │   └── settings.py           # Pydantic settings
│       ├── grpc/
│       │   ├── __init__.py
│       │   ├── server.py             # gRPC service implementation
│       │   ├── servicer.py           # gRPC servicer
│       │   └── proto/                # Generated protobuf files
│       │       ├── __init__.py
│       │       ├── collector_pb2.py
│       │       └── collector_pb2_grpc.py
│       ├── rest/
│       │   ├── __init__.py
│       │   ├── api.py                # FastAPI application
│       │   └── routes/
│       │       ├── __init__.py
│       │       ├── templates.py      # Template management endpoints
│       │       └── configs.py        # Config management endpoints
│       ├── storage/
│       │   ├── __init__.py
│       │   ├── base.py               # Abstract storage interface
│       │   ├── memory.py             # In-memory storage
│       │   └── redis.py              # Redis storage implementation
│       ├── templates/
│       │   ├── __init__.py
│       │   └── engine.py             # Jinja2 template processing
│       └── utils/
│           ├── __init__.py
│           ├── logging.py            # Structured logging setup
│           └── lifecycle.py          # Application lifecycle management
├── tests/
│   ├── __init__.py
│   ├── conftest.py                   # Pytest configuration
│   ├── test_grpc.py                  # gRPC service tests
│   ├── test_rest.py                  # REST API tests
│   ├── test_storage.py               # Storage layer tests
│   ├── test_templates.py             # Template engine tests
│   └── integration/
│       ├── __init__.py
│       └── test_alloy_compatibility.py
└── conf/
    └── default.conf.j2               # Jinja2 template (converted)
```

## Component Design

### 1. Configuration Management (`config/settings.py`)

```python
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Server Configuration
    grpc_port: int = 8888
    http_port: int = 8080
    config_folder: str = "conf"
    
    # Storage Configuration
    use_redis: bool = False
    redis_url: Optional[str] = None
    redis_ttl: int = 259200
    
    # Organization
    org_name: str = "default"
    
    # Logging
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
```

### 2. gRPC Service (`grpc/servicer.py`)

```python
import grpc
from grpc import aio
from .proto import collector_pb2, collector_pb2_grpc
from ..templates.engine import TemplateEngine
from ..storage.base import StorageInterface

class CollectorServicer(collector_pb2_grpc.CollectorServiceServicer):
    def __init__(self, template_engine: TemplateEngine, storage: StorageInterface):
        self.template_engine = template_engine
        self.storage = storage
    
    async def GetConfig(
        self, 
        request: collector_pb2.GetConfigRequest, 
        context: grpc.aio.ServicerContext
    ) -> collector_pb2.GetConfigResponse:
        # Extract metadata
        config_id = request.id
        attributes = dict(request.local_attributes or request.attributes)
        
        # Determine template
        template_name = attributes.get("template", "default")
        
        # Render configuration
        metadata = {"Id": config_id, "Attributes": attributes}
        content = await self.template_engine.render(template_name, metadata)
        
        # Store in cache
        await self.storage.set(config_id, content)
        
        return collector_pb2.GetConfigResponse(content=content)
```

### 3. Template Engine (`templates/engine.py`)

```python
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
from typing import Dict, Any
import aiofiles
import os

class TemplateEngine:
    def __init__(self, template_folder: str):
        self.template_folder = template_folder
        self.env = Environment(
            loader=FileSystemLoader(template_folder),
            enable_async=True
        )
    
    async def load_templates(self) -> None:
        """Load all .j2 templates from the template folder"""
        # Implementation to scan and validate templates
        pass
    
    async def render(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render a template with the given context"""
        try:
            template = self.env.get_template(f"{template_name}.conf.j2")
            return await template.render_async(**context)
        except TemplateNotFound:
            # Fallback to default template
            template = self.env.get_template("default.conf.j2")
            return await template.render_async(**context)
```

### 4. Storage Interface (`storage/base.py`)

```python
from abc import ABC, abstractmethod
from typing import List, Optional

class StorageInterface(ABC):
    @abstractmethod
    async def set(self, key: str, value: str) -> None:
        """Store a configuration"""
        pass
    
    @abstractmethod
    async def get(self, key: str) -> Optional[str]:
        """Retrieve a configuration"""
        pass
    
    @abstractmethod
    async def get_all(self) -> List[str]:
        """Get all configuration keys"""
        pass
    
    @abstractmethod
    async def close(self) -> None:
        """Clean up resources"""
        pass
```

### 5. FastAPI REST API (`rest/api.py`)

```python
from fastapi import FastAPI, HTTPException
from fastapi.responses import PlainTextResponse
from ..storage.base import StorageInterface
from ..templates.engine import TemplateEngine

def create_app(storage: StorageInterface, template_engine: TemplateEngine) -> FastAPI:
    app = FastAPI(title="Alloy Remote Config Server", version="1.0.0")
    
    @app.get("/templates")
    async def list_templates():
        return await template_engine.list_templates()
    
    @app.get("/configs")
    async def list_configs():
        return await storage.get_all()
    
    @app.get("/configs/{config_id}", response_class=PlainTextResponse)
    async def get_config(config_id: str):
        config = await storage.get(config_id)
        if config is None:
            raise HTTPException(status_code=404, detail="Config not found")
        return config
    
    return app
```

## Key Improvements Over Go Version

### 1. Async/Await Support
- Native asyncio for concurrent request handling
- Async Redis operations
- Async template rendering

### 2. Type Safety
- Pydantic models for configuration validation
- Type hints throughout the codebase
- MyPy static type checking

### 3. Better Error Handling
- Structured error responses
- Proper HTTP status codes
- gRPC status codes

### 4. Enhanced Templating
- Jinja2 provides more features than Go templates
- Better error messages for template issues
- Template inheritance and macros support

### 5. Observability
- Structured logging with structlog
- Metrics integration ready
- Health check endpoints

## Migration Considerations

### Template Syntax Changes
Go templates use `{{ .Id }}` while Jinja2 uses `{{ Id }}`. A migration script will handle this conversion.

### gRPC Compatibility
The Python gRPC implementation will be fully compatible with the Connect protocol used by the Go version.

### Performance
- Async operations should provide better concurrency
- Redis connection pooling for better resource utilization
- Template caching for improved performance

## Deployment Strategy

### Docker Container
Multi-stage build similar to Go version:
1. Build stage: Install dependencies and generate protobuf files
2. Runtime stage: Minimal Python runtime with application

### Configuration
Environment variables remain the same for easy migration:
- `GRPC_PORT`, `HTTP_PORT`, `CONFIG_FOLDER`
- `USE_REDIS`, `REDIS_URL`, `REDIS_TTL`
- `ORG_NAME`
