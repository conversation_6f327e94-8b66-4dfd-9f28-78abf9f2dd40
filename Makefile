.PHONY: help install dev-install proto clean test lint format check docker-build docker-run

# Default target
help:
	@echo "Available targets:"
	@echo "  install      - Install production dependencies"
	@echo "  dev-install  - Install development dependencies"
	@echo "  proto        - Generate protobuf files"
	@echo "  clean        - Clean generated files"
	@echo "  test         - Run tests"
	@echo "  lint         - Run linting"
	@echo "  format       - Format code"
	@echo "  check        - Run all checks (lint, type check, test)"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"

# Installation
install:
	poetry install --only=main

dev-install:
	poetry install
	poetry run pre-commit install

# Protobuf generation
proto:
	poetry run python scripts/generate_proto.py

# Cleaning
clean:
	rm -rf src/alloy_config_server/grpc/proto/*
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	rm -rf .pytest_cache
	rm -rf .mypy_cache
	rm -rf .ruff_cache
	rm -rf dist/
	rm -rf build/

# Testing
test:
	poetry run pytest

test-verbose:
	poetry run pytest -v

test-coverage:
	poetry run pytest --cov=alloy_config_server --cov-report=html --cov-report=term

# Code quality
lint:
	poetry run ruff check .
	poetry run mypy .

format:
	poetry run black .
	poetry run ruff check --fix .

check: lint test

# Docker
docker-build:
	docker build -t alloy-remote-config-server-py .

docker-run:
	docker run -p 8080:8080 -p 8888:8888 alloy-remote-config-server-py

# Development server
dev-server:
	poetry run python -m alloy_config_server.main

# Generate requirements.txt for Docker
requirements:
	poetry export -f requirements.txt --output requirements.txt --without-hashes
