"""Redis storage implementation."""

import asyncio
from typing import List, Optional
import redis.asyncio as redis
from redis.asyncio import Redis
from .base import StorageInterface


class RedisStorage(StorageInterface):
    """Redis storage for configurations."""
    
    def __init__(
        self, 
        redis_client: Redis, 
        org_name: str = "default", 
        ttl: int = 259200
    ) -> None:
        """Initialize Redis storage.
        
        Args:
            redis_client: Async Redis client
            org_name: Organization name for key namespacing
            ttl: Time-to-live in seconds (default: 3 days)
        """
        self.redis = redis_client
        self.org_name = org_name
        self.ttl = ttl
    
    @classmethod
    async def create(
        cls, 
        redis_url: str, 
        org_name: str = "default", 
        ttl: int = 259200,
        **redis_kwargs
    ) -> "RedisStorage":
        """Create Redis storage with connection.
        
        Args:
            redis_url: Redis connection URL
            org_name: Organization name
            ttl: Time-to-live in seconds
            **redis_kwargs: Additional Redis client arguments
            
        Returns:
            Configured RedisStorage instance
        """
        redis_client = redis.from_url(
            redis_url,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            **redis_kwargs
        )
        
        # Test connection
        await redis_client.ping()
        
        return cls(redis_client, org_name, ttl)
    
    async def set(self, key: str, value: str) -> None:
        """Store a configuration in Redis.
        
        Args:
            key: Configuration identifier
            value: Configuration content
        """
        formatted_key = self.format_key(self.org_name, key)
        
        # Set value and TTL in a pipeline for atomicity
        async with self.redis.pipeline() as pipe:
            await pipe.set(formatted_key, value)
            await pipe.expire(formatted_key, self.ttl)
            await pipe.execute()
    
    async def get(self, key: str) -> Optional[str]:
        """Retrieve a configuration from Redis.
        
        Args:
            key: Configuration identifier
            
        Returns:
            Configuration content or None if not found
        """
        formatted_key = self.format_key(self.org_name, key)
        return await self.redis.get(formatted_key)
    
    async def get_all(self) -> List[str]:
        """Get all configuration keys.
        
        Returns:
            List of configuration identifiers (without org prefix)
        """
        org_prefix = f"{{{self.org_name}}}:"
        pattern = f"{org_prefix}*"
        
        keys = []
        async for key in self.redis.scan_iter(match=pattern, count=100):
            keys.append(key.removeprefix(org_prefix))
        
        return keys
    
    async def close(self) -> None:
        """Close Redis connection."""
        await self.redis.aclose()
    
    async def health_check(self) -> bool:
        """Check if Redis is healthy.
        
        Returns:
            True if Redis is responding, False otherwise
        """
        try:
            await self.redis.ping()
            return True
        except Exception:
            return False
