"""In-memory storage implementation."""

from typing import Dict, List, Optional
from .base import StorageInterface


class MemoryStorage(StorageInterface):
    """In-memory storage for configurations."""
    
    def __init__(self, org_name: str = "default") -> None:
        """Initialize memory storage.
        
        Args:
            org_name: Organization name for key namespacing
        """
        self.org_name = org_name
        self._store: Dict[str, str] = {}
    
    async def set(self, key: str, value: str) -> None:
        """Store a configuration in memory.
        
        Args:
            key: Configuration identifier
            value: Configuration content
        """
        formatted_key = self.format_key(self.org_name, key)
        self._store[formatted_key] = value
    
    async def get(self, key: str) -> Optional[str]:
        """Retrieve a configuration from memory.
        
        Args:
            key: Configuration identifier
            
        Returns:
            Configuration content or None if not found
        """
        formatted_key = self.format_key(self.org_name, key)
        return self._store.get(formatted_key)
    
    async def get_all(self) -> List[str]:
        """Get all configuration keys.
        
        Returns:
            List of configuration identifiers (without org prefix)
        """
        org_prefix = f"{{{self.org_name}}}:"
        return [
            key.removeprefix(org_prefix)
            for key in self._store.keys()
            if key.startswith(org_prefix)
        ]
    
    async def close(self) -> None:
        """Clean up resources (no-op for memory storage)."""
        pass
    
    def clear(self) -> None:
        """Clear all stored configurations (useful for testing)."""
        self._store.clear()
