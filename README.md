
# (DOE Observability README)

## About

2025-06 copied from:

https://github.com/opsplane-services/alloy-remote-config-server

This is an implementation of a Remote Configuration Serve that can respond to gRPC requests
from Grafana Alloy agents, where the Alloy configuration looks like:

```
remotecfg {

    url = "http://alloyrc.obs.nsw.education"

    id             = constants.hostname

    attributes     = {"cluster" = "prod"}

    poll_frequency = "30m"

}

```



## Configuration & Installation

Initial commit of this repository is a verbatim snapshot of upstream, taken at 2025-06-30,
where the author's last commit was around 8 months earlier.

Second commit are the basics required to get this working with:
- Alloy 1.9.x - with the `s/attributes/localAttributes` syntax change
- alloy-remote-config golang module 0.0.10
- promiscuous binding so Nomad can route to the container interfaces


### Build process

You can follow the upstream, but this gives you the full whoa-to-go.

Note that you need golang 1.21 or better (to provide `toolchain` capability).


```
cd ~/src

<NAME_EMAIL>:nsw-education/obs-alloy-remote-config-server.git

cd obs-alloy-remote-config-server

docker build -t alloy-remote-config-server .

# Adjust target to taste - I'm using v0.1.1 as upstream has this as v0.1.0
#        https://hub.docker.com/r/opsplane/alloy-remote-config-server/tags

skopeo  copy                                                                       \
        docker-daemon:alloy-remote-config-server:latest                            \
        docker://quay.education.nsw.gov.au/observability/alloy-remote-config-server:v0.1.1
```

