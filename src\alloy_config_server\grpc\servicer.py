"""gRPC CollectorService implementation."""

import hashlib
from typing import Dict, Any
import grpc
import structlog

# Import will be available after protobuf generation
try:
    from .proto.collector.v1 import collector_pb2, collector_pb2_grpc
except ImportError:
    # Fallback for development before proto generation
    collector_pb2 = None
    collector_pb2_grpc = None

from ..storage.base import StorageInterface
from ..templates.engine import TemplateEngine

logger = structlog.get_logger(__name__)


class CollectorServicer:
    """gRPC servicer implementing the CollectorService."""
    
    def __init__(self, template_engine: TemplateEngine, storage: StorageInterface) -> None:
        """Initialize the servicer.
        
        Args:
            template_engine: Template engine for rendering configurations
            storage: Storage backend for caching configurations
        """
        self.template_engine = template_engine
        self.storage = storage
    
    async def GetConfig(
        self, 
        request, # collector_pb2.GetConfigRequest
        context: grpc.aio.ServicerContext
    ):
        """Handle GetConfig gRPC requests.
        
        Args:
            request: GetConfigRequest containing collector ID and attributes
            context: gRPC service context
            
        Returns:
            GetConfigResponse with rendered configuration
        """
        if collector_pb2 is None:
            await context.abort(grpc.StatusCode.INTERNAL, "Protobuf not generated")
            return
        
        try:
            # Extract request data
            config_id = request.id
            
            # Use local_attributes if available, fallback to deprecated attributes
            attributes = dict(request.local_attributes) if request.local_attributes else dict(request.attributes)
            request_hash = request.hash
            
            logger.info(
                "GetConfig request received",
                config_id=config_id,
                attributes=attributes,
                hash=request_hash
            )
            
            # Determine template name
            template_name = attributes.get("template", "default")
            
            # Prepare template context (matching Go implementation structure)
            template_context = {
                "Id": config_id,
                "Attributes": attributes
            }
            
            # Render configuration
            try:
                content = await self.template_engine.render(template_name, template_context)
            except Exception as e:
                logger.error(
                    "Template rendering failed",
                    config_id=config_id,
                    template=template_name,
                    error=str(e)
                )
                await context.abort(
                    grpc.StatusCode.INTERNAL, 
                    f"Template rendering failed: {str(e)}"
                )
                return
            
            # Generate content hash
            content_hash = hashlib.sha256(content.encode()).hexdigest()[:16]
            
            # Check if content has changed
            not_modified = request_hash == content_hash
            
            if not not_modified:
                # Store in cache only if content changed
                try:
                    await self.storage.set(config_id, content)
                    logger.debug(
                        "Configuration cached",
                        config_id=config_id,
                        content_length=len(content)
                    )
                except Exception as e:
                    logger.warning(
                        "Failed to cache configuration",
                        config_id=config_id,
                        error=str(e)
                    )
                    # Don't fail the request if caching fails
            
            logger.info(
                "GetConfig request completed",
                config_id=config_id,
                template=template_name,
                content_length=len(content),
                hash=content_hash,
                not_modified=not_modified
            )
            
            return collector_pb2.GetConfigResponse(
                content=content,
                hash=content_hash,
                not_modified=not_modified
            )
            
        except Exception as e:
            logger.error(
                "GetConfig request failed",
                config_id=getattr(request, 'id', 'unknown'),
                error=str(e),
                exc_info=True
            )
            await context.abort(
                grpc.StatusCode.INTERNAL, 
                f"Internal server error: {str(e)}"
            )
    
    async def RegisterCollector(
        self, 
        request, # collector_pb2.RegisterCollectorRequest
        context: grpc.aio.ServicerContext
    ):
        """Handle RegisterCollector gRPC requests.
        
        Args:
            request: RegisterCollectorRequest
            context: gRPC service context
            
        Returns:
            RegisterCollectorResponse (empty)
        """
        if collector_pb2 is None:
            await context.abort(grpc.StatusCode.INTERNAL, "Protobuf not generated")
            return
        
        config_id = request.id
        name = request.name
        attributes = dict(request.local_attributes) if request.local_attributes else dict(request.attributes)
        
        logger.info(
            "RegisterCollector request received",
            config_id=config_id,
            name=name,
            attributes=attributes
        )
        
        # Note: Registration is not actively used in the Go implementation
        # Agents are effectively registered when they call GetConfig
        
        return collector_pb2.RegisterCollectorResponse()
    
    async def UnregisterCollector(
        self, 
        request, # collector_pb2.UnregisterCollectorRequest
        context: grpc.aio.ServicerContext
    ):
        """Handle UnregisterCollector gRPC requests.
        
        Args:
            request: UnregisterCollectorRequest
            context: gRPC service context
            
        Returns:
            UnregisterCollectorResponse (empty)
        """
        if collector_pb2 is None:
            await context.abort(grpc.StatusCode.INTERNAL, "Protobuf not generated")
            return
        
        config_id = request.id
        
        logger.info(
            "UnregisterCollector request received",
            config_id=config_id
        )
        
        # Note: Unregistration is not actively used in the Go implementation
        # Agents are unregistered when not accessed for a long time (TTL)
        
        return collector_pb2.UnregisterCollectorResponse()
