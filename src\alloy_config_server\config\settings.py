"""Application settings using Pydantic."""

from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Server Configuration
    grpc_port: int = Field(default=8888, description="gRPC server port")
    http_port: int = Field(default=8080, description="HTTP server port")
    config_folder: str = Field(default="conf", description="Template folder path")
    
    # Storage Configuration
    use_redis: bool = Field(default=False, description="Use Redis for storage")
    redis_url: Optional[str] = Field(default=None, description="Redis connection URL")
    redis_ttl: int = Field(default=259200, description="Redis TTL in seconds (3 days)")
    
    # Organization
    org_name: str = Field(default="default", description="Organization namespace")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")
    
    # Development
    debug: bool = Field(default=False, description="Enable debug mode")
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore",
    }
    
    def get_redis_config(self) -> dict:
        """Get Redis configuration dictionary."""
        if not self.use_redis or not self.redis_url:
            return {}
        
        return {
            "url": self.redis_url,
            "decode_responses": True,
            "socket_connect_timeout": 5,
            "socket_timeout": 5,
            "retry_on_timeout": True,
        }
