"""Tests for storage implementations."""

import pytest
from alloy_config_server.storage import MemoryStorage


class TestMemoryStorage:
    """Test memory storage implementation."""
    
    @pytest.mark.asyncio
    async def test_set_and_get(self, memory_storage: MemoryStorage):
        """Test setting and getting configurations."""
        # Test setting a configuration
        await memory_storage.set("test-id", "test-config-content")
        
        # Test getting the configuration
        result = await memory_storage.get("test-id")
        assert result == "test-config-content"
    
    @pytest.mark.asyncio
    async def test_get_nonexistent(self, memory_storage: MemoryStorage):
        """Test getting non-existent configuration."""
        result = await memory_storage.get("nonexistent")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_all_empty(self, memory_storage: MemoryStorage):
        """Test getting all configurations when empty."""
        result = await memory_storage.get_all()
        assert result == []
    
    @pytest.mark.asyncio
    async def test_get_all_with_data(self, memory_storage: MemoryStorage):
        """Test getting all configurations with data."""
        # Add some configurations
        await memory_storage.set("config1", "content1")
        await memory_storage.set("config2", "content2")
        
        # Get all configurations
        result = await memory_storage.get_all()
        assert set(result) == {"config1", "config2"}
    
    @pytest.mark.asyncio
    async def test_organization_namespacing(self):
        """Test organization namespacing."""
        storage1 = MemoryStorage(org_name="org1")
        storage2 = MemoryStorage(org_name="org2")
        
        try:
            # Set same key in different organizations
            await storage1.set("test", "content1")
            await storage2.set("test", "content2")
            
            # Should get different values
            result1 = await storage1.get("test")
            result2 = await storage2.get("test")
            
            assert result1 == "content1"
            assert result2 == "content2"
            
            # Should not see each other's keys
            all1 = await storage1.get_all()
            all2 = await storage2.get_all()
            
            assert all1 == ["test"]
            assert all2 == ["test"]
            
        finally:
            await storage1.close()
            await storage2.close()
    
    @pytest.mark.asyncio
    async def test_clear(self, memory_storage: MemoryStorage):
        """Test clearing storage."""
        # Add some data
        await memory_storage.set("test1", "content1")
        await memory_storage.set("test2", "content2")
        
        # Verify data exists
        assert await memory_storage.get("test1") == "content1"
        assert len(await memory_storage.get_all()) == 2
        
        # Clear storage
        memory_storage.clear()
        
        # Verify data is gone
        assert await memory_storage.get("test1") is None
        assert await memory_storage.get_all() == []
