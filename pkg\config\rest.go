package config

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func StartRestServer(port int) {
	r := gin.Default()

	r.GET("/templates", func(c *gin.Context) {
		templateList := make([]string, 0)
		for name := range templates {
			templateList = append(templateList, name)
		}
		c.<PERSON>(http.StatusOK, templateList)
	})

	r.GE<PERSON>("/configs", func(c *gin.Context) {
		configs, err := globalStorage.GetAll()
		if err != nil {
			c.JSON(http.StatusBadRequest, err)
			return
		}
		c.<PERSON>(http.StatusOK, configs)
	})

	r.GET("/configs/:id", func(c *gin.Context) {
		id := c.Param("id")
		config, err := globalStorage.Get(id)
		if err != nil {
			c.JSON(http.StatusBadRequest, err)
			return
		}
		c.<PERSON>(http.StatusOK, "text/plain; charset=utf-8", []byte(config))
	})

  // jedd - bind promiscuously
	// r.Run(fmt.Sprintf("127.0.0.1:%d", port))
	r.Run(fmt.Sprintf("0.0.0.0:%d", port))
}
