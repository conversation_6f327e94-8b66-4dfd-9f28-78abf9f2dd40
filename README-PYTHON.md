# Alloy Remote Config Server - Python Implementation

A Python implementation of the Grafana Alloy Remote Configuration Server, providing full compatibility with Grafana Alloy agents while leveraging Python's strengths.

## Features

- **Full Alloy Compatibility**: Implements the official Grafana Alloy Remote Config gRPC protocol
- **Async Architecture**: Built with asyncio for high-performance concurrent request handling
- **Flexible Storage**: Supports both in-memory and Redis storage backends
- **Powerful Templating**: Uses Jinja2 for more advanced template features than Go templates
- **Type Safety**: Full type hints and Pydantic validation
- **Observability**: Structured logging with JSON output
- **Easy Deployment**: Docker containers and docker-compose setup
- **Comprehensive Testing**: Unit and integration tests with pytest

## Quick Start

### Prerequisites

- Python 3.11+
- Poetry (for dependency management)
- Docker (optional, for containerized deployment)

### Installation

1. **Clone and setup**:
   ```bash
   git checkout python-refactor
   cd obs-alloy-remote-config-server
   ```

2. **Install dependencies**:
   ```bash
   make dev-install
   ```

3. **Generate protobuf files**:
   ```bash
   make proto
   ```

4. **Run the server**:
   ```bash
   make dev-server
   ```

The server will start with:
- gRPC service on port 8888
- HTTP API on port 8080

### Docker Deployment

1. **Build and run with Docker Compose**:
   ```bash
   docker-compose up alloy-config-server
   ```

2. **With Redis backend**:
   ```bash
   docker-compose up alloy-config-server-redis redis
   ```

## Configuration

Configure via environment variables or `.env` file:

```bash
# Server Configuration
GRPC_PORT=8888
HTTP_PORT=8080
CONFIG_FOLDER=conf

# Storage Configuration
USE_REDIS=false
REDIS_URL=redis://localhost:6379/0
REDIS_TTL=259200

# Organization
ORG_NAME=default

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
```

## Template Migration

Templates are migrated from Go template syntax to Jinja2:

**Go Template** (`default.conf.tmpl`):
```go
// Comment: {{ .Id }}
prometheus.exporter.unix "default" {
    // Cluster: {{ .Attributes.cluster }}
}
```

**Jinja2 Template** (`default.conf.j2`):
```jinja2
// Comment: {{ Id }}
prometheus.exporter.unix "default" {
    // Cluster: {{ Attributes.cluster }}
}
```

Key differences:
- Remove the leading dot: `.Id` → `Id`
- File extension: `.conf.tmpl` → `.conf.j2`
- More powerful features available (loops, conditionals, filters)

## API Endpoints

### gRPC Service (Port 8888)
- `GetConfig` - Get configuration for an Alloy agent
- `RegisterCollector` - Register an agent (no-op, for compatibility)
- `UnregisterCollector` - Unregister an agent (no-op, for compatibility)

### HTTP API (Port 8080)
- `GET /health` - Health check
- `GET /templates` - List available templates
- `GET /configs` - List cached configurations
- `GET /configs/{id}` - Get specific cached configuration
- `POST /render/{template}` - Render template (debug mode only)

## Development

### Running Tests
```bash
make test                # Run all tests
make test-verbose        # Verbose output
make test-coverage       # With coverage report
```

### Code Quality
```bash
make lint               # Run linting
make format             # Format code
make check              # Run all checks
```

### Development Server
```bash
make dev-server         # Run development server
```

## Architecture

### Components

1. **gRPC Service** (`alloy_config_server.grpc`)
   - Implements CollectorService protocol
   - Async request handling
   - Error handling and logging

2. **REST API** (`alloy_config_server.rest`)
   - FastAPI-based HTTP endpoints
   - Management and debugging interface
   - Health checks

3. **Template Engine** (`alloy_config_server.templates`)
   - Jinja2-based template processing
   - Async template rendering
   - Template validation and caching

4. **Storage Layer** (`alloy_config_server.storage`)
   - Abstract storage interface
   - Memory and Redis implementations
   - Organization namespacing

5. **Configuration** (`alloy_config_server.config`)
   - Pydantic-based settings
   - Environment variable loading
   - Type validation

### Key Improvements Over Go Version

1. **Better Async Support**: Native asyncio for handling concurrent requests
2. **Superior Templating**: Jinja2 offers more features than Go templates
3. **Type Safety**: Pydantic for runtime type validation
4. **Rich Ecosystem**: Extensive Python libraries for observability
5. **Testing Framework**: pytest provides excellent testing capabilities

## Migration from Go Version

1. **Template Conversion**: Convert `.conf.tmpl` files to `.conf.j2` format
2. **Environment Variables**: Same environment variables are supported
3. **Docker Deployment**: Similar Docker setup with Python runtime
4. **Gradual Rollout**: Can run alongside Go version for testing

## Compatibility

- **Grafana Alloy**: Compatible with Alloy 1.9.x and later
- **Protocol**: Implements alloy-remote-config v0.0.10 protocol
- **Storage**: Redis configurations are compatible between versions

## Performance

- **Async Architecture**: Better handling of concurrent requests
- **Connection Pooling**: Efficient Redis connection management
- **Template Caching**: Templates are compiled and cached
- **Memory Efficiency**: Optimized for long-running deployments

## Monitoring

The server provides structured JSON logging and health endpoints:

```bash
# Health check
curl http://localhost:8080/health

# List templates
curl http://localhost:8080/templates

# List cached configs
curl http://localhost:8080/configs
```

## Contributing

1. Install development dependencies: `make dev-install`
2. Run tests: `make test`
3. Check code quality: `make check`
4. Generate protobuf files: `make proto`

## License

Same license as the original Go implementation.
