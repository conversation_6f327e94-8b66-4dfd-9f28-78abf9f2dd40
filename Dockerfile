FROM golang:1.22-alpine

ENV GO111MODULE=on
RUN apk add --no-cache git
ADD . /go/src/gitlab.com/opsplane-services/alloy-remote-config-server
WORKDIR /go/src/gitlab.com/opsplane-services/alloy-remote-config-server
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o /alloy-remote-config-server cmd/config/main.go

# Use a more feature-rich base image for debugging
FROM alpine:3.16.0

# Install debugging and troubleshooting tools
RUN apk add --no-cache \
    ca-certificates \
    bash \
    curl \
    wget \
    net-tools \
    lsof \
    procps \
    tcpdump \
    strace \
    bind-tools \
    iputils

# Copy the binary
COPY --from=0 /alloy-remote-config-server /

# Set bash as default shell
SHELL ["/bin/bash", "-c"]

CMD ["/alloy-remote-config-server"]
