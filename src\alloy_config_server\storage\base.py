"""Abstract storage interface."""

from abc import ABC, abstractmethod
from typing import List, Optional


class StorageInterface(ABC):
    """Abstract interface for configuration storage."""
    
    @abstractmethod
    async def set(self, key: str, value: str) -> None:
        """Store a configuration.
        
        Args:
            key: Configuration identifier
            value: Configuration content
        """
        pass
    
    @abstractmethod
    async def get(self, key: str) -> Optional[str]:
        """Retrieve a configuration.
        
        Args:
            key: Configuration identifier
            
        Returns:
            Configuration content or None if not found
        """
        pass
    
    @abstractmethod
    async def get_all(self) -> List[str]:
        """Get all configuration keys.
        
        Returns:
            List of configuration identifiers
        """
        pass
    
    @abstractmethod
    async def close(self) -> None:
        """Clean up resources."""
        pass
    
    def format_key(self, org_name: str, config_id: str) -> str:
        """Format storage key with organization namespace.
        
        Args:
            org_name: Organization name
            config_id: Configuration ID
            
        Returns:
            Formatted key
        """
        return f"{{{org_name}}}:{config_id}"
