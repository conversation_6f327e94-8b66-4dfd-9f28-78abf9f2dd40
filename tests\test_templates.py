"""Tests for template engine."""

import pytest
from jinja2 import TemplateNotFound
from alloy_config_server.templates import TemplateEngine


class TestTemplateEngine:
    """Test template engine implementation."""
    
    @pytest.mark.asyncio
    async def test_load_templates(self, template_engine: TemplateEngine):
        """Test loading templates."""
        templates = template_engine.list_templates()
        assert "default" in templates
        assert "custom" in templates
        assert len(templates) == 2
    
    @pytest.mark.asyncio
    async def test_render_default_template(self, template_engine: TemplateEngine):
        """Test rendering default template."""
        context = {
            "Id": "test-host",
            "Attributes": {"cluster": "prod", "env": "production"}
        }
        
        result = await template_engine.render("default", context)
        
        assert "test-host" in result
        assert "prometheus.exporter.unix" in result
        assert "Attributes" in result
    
    @pytest.mark.asyncio
    async def test_render_custom_template(self, template_engine: TemplateEngine):
        """Test rendering custom template."""
        context = {
            "Id": "test-host",
            "Attributes": {"cluster": "prod", "env": "production"}
        }
        
        result = await template_engine.render("custom", context)
        
        assert "test-host" in result
        assert "cluster: prod" in result
        assert "env: production" in result
        assert "loki.echo" in result
    
    @pytest.mark.asyncio
    async def test_render_nonexistent_template_fallback(self, template_engine: TemplateEngine):
        """Test rendering non-existent template falls back to default."""
        context = {
            "Id": "test-host",
            "Attributes": {}
        }
        
        # Should fall back to default template
        result = await template_engine.render("nonexistent", context)
        
        assert "test-host" in result
        assert "prometheus.exporter.unix" in result
    
    @pytest.mark.asyncio
    async def test_has_template(self, template_engine: TemplateEngine):
        """Test checking if template exists."""
        assert template_engine.has_template("default")
        assert template_engine.has_template("custom")
        assert not template_engine.has_template("nonexistent")
    
    @pytest.mark.asyncio
    async def test_render_with_empty_context(self, template_engine: TemplateEngine):
        """Test rendering with minimal context."""
        context = {
            "Id": "minimal-host",
            "Attributes": {}
        }
        
        result = await template_engine.render("default", context)
        
        assert "minimal-host" in result
        assert "prometheus.exporter.unix" in result
