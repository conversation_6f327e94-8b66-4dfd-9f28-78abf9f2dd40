"""gRPC server implementation."""

import asyncio
from typing import Optional
import grpc
from grpc import aio
import structlog

# Import will be available after protobuf generation
try:
    from .proto.collector.v1 import collector_pb2_grpc
except ImportError:
    collector_pb2_grpc = None

from .servicer import CollectorServicer
from ..storage.base import StorageInterface
from ..templates.engine import TemplateEngine

logger = structlog.get_logger(__name__)


class GrpcServer:
    """Async gRPC server for the CollectorService."""
    
    def __init__(
        self, 
        template_engine: TemplateEngine, 
        storage: StorageInterface,
        port: int = 8888,
        bind_address: str = "0.0.0.0"
    ) -> None:
        """Initialize gRPC server.
        
        Args:
            template_engine: Template engine for rendering configurations
            storage: Storage backend for caching
            port: Port to bind the server to
            bind_address: Address to bind the server to
        """
        self.template_engine = template_engine
        self.storage = storage
        self.port = port
        self.bind_address = bind_address
        self.server: Optional[aio.Server] = None
        self.servicer = CollectorServicer(template_engine, storage)
    
    async def start(self) -> None:
        """Start the gRPC server."""
        if collector_pb2_grpc is None:
            raise RuntimeError("Protobuf files not generated. Run 'make proto' first.")
        
        self.server = aio.server()
        
        # Add the servicer to the server
        collector_pb2_grpc.add_CollectorServiceServicer_to_server(
            self.servicer, 
            self.server
        )
        
        # Configure server address
        listen_addr = f"{self.bind_address}:{self.port}"
        self.server.add_insecure_port(listen_addr)
        
        # Start the server
        await self.server.start()
        
        logger.info(
            "gRPC server started",
            address=listen_addr,
            port=self.port
        )
    
    async def stop(self, grace_period: float = 5.0) -> None:
        """Stop the gRPC server.
        
        Args:
            grace_period: Time to wait for graceful shutdown
        """
        if self.server is None:
            return
        
        logger.info("Stopping gRPC server")
        
        # Stop accepting new requests and wait for existing ones to complete
        await self.server.stop(grace_period)
        
        logger.info("gRPC server stopped")
    
    async def wait_for_termination(self) -> None:
        """Wait for the server to terminate."""
        if self.server is None:
            return
        
        await self.server.wait_for_termination()
    
    async def serve_forever(self) -> None:
        """Start the server and wait for termination."""
        await self.start()
        
        try:
            await self.wait_for_termination()
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
        finally:
            await self.stop()
    
    def is_running(self) -> bool:
        """Check if the server is running.
        
        Returns:
            True if server is running, False otherwise
        """
        return self.server is not None
