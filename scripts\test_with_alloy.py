#!/usr/bin/env python3
"""Script to test Python server with a real Alloy agent."""

import asyncio
import subprocess
import time
import requests
import tempfile
from pathlib import Path


async def test_alloy_integration():
    """Test the Python server with a real Alloy agent."""
    print("🧪 Testing Python Alloy Remote Config Server with real Alloy agent")
    
    # Create temporary Alloy config
    alloy_config = """
logging {
    level  = "debug"
    format = "logfmt"
}

remotecfg {
    url            = "http://127.0.0.1:8888"
    id             = "test-python-agent"
    local_attributes = {
        "template" = "default",
        "cluster"  = "test",
        "env"      = "integration-test"
    }
    poll_frequency = "10s"
}
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.alloy', delete=False) as f:
        f.write(alloy_config)
        alloy_config_path = f.name
    
    try:
        # Start Python server
        print("🚀 Starting Python server...")
        server_process = subprocess.Popen([
            "python", "-m", "alloy_config_server.main"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        print("⏳ Waiting for server to start...")
        await asyncio.sleep(5)
        
        # Check server health
        try:
            response = requests.get("http://localhost:8080/health", timeout=5)
            if response.status_code == 200:
                print("✅ Python server is healthy")
                print(f"   Health status: {response.json()}")
            else:
                print(f"❌ Server health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Failed to connect to server: {e}")
            return False
        
        # Check templates
        try:
            response = requests.get("http://localhost:8080/templates", timeout=5)
            if response.status_code == 200:
                templates = response.json()
                print(f"✅ Templates loaded: {templates}")
            else:
                print(f"❌ Failed to get templates: {response.status_code}")
        except Exception as e:
            print(f"❌ Failed to get templates: {e}")
        
        # Start Alloy agent (if available)
        print("🔧 Attempting to start Alloy agent...")
        try:
            alloy_process = subprocess.Popen([
                "alloy", "run", alloy_config_path,
                "--server.http.listen-addr=127.0.0.1:12345"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            print("⏳ Waiting for Alloy agent to connect...")
            await asyncio.sleep(10)
            
            # Check if configuration was requested
            response = requests.get("http://localhost:8080/configs", timeout=5)
            if response.status_code == 200:
                configs = response.json()
                if "test-python-agent" in configs:
                    print("✅ Alloy agent successfully requested configuration!")
                    
                    # Get the actual config
                    config_response = requests.get(
                        "http://localhost:8080/configs/test-python-agent", 
                        timeout=5
                    )
                    if config_response.status_code == 200:
                        print("✅ Configuration retrieved:")
                        print("   " + "\n   ".join(config_response.text.split("\n")[:5]))
                        print("   ...")
                else:
                    print("⚠️  No configuration requests from Alloy agent yet")
                    print(f"   Available configs: {configs}")
            
            alloy_process.terminate()
            alloy_process.wait(timeout=5)
            
        except FileNotFoundError:
            print("⚠️  Alloy binary not found, skipping agent test")
            print("   Install Alloy to test full integration")
        except Exception as e:
            print(f"⚠️  Alloy agent test failed: {e}")
        
        print("✅ Integration test completed")
        return True
        
    finally:
        # Cleanup
        if server_process:
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                server_process.kill()
        
        # Remove temp file
        Path(alloy_config_path).unlink(missing_ok=True)


def main():
    """Main function."""
    try:
        result = asyncio.run(test_alloy_integration())
        if result:
            print("\n🎉 Integration test passed!")
        else:
            print("\n❌ Integration test failed!")
            exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        exit(1)


if __name__ == "__main__":
    main()
