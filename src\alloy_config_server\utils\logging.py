"""Structured logging setup."""

import sys
import structlog
from structlog.typing import FilteringBoundLogger


def setup_logging(level: str = "INFO", format_type: str = "json") -> FilteringBoundLogger:
    """Setup structured logging with structlog.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
        format_type: Log format ("json" or "text")
        
    Returns:
        Configured logger
    """
    # Configure structlog
    if format_type.lower() == "json":
        renderer = structlog.processors.JSONRenderer()
    else:
        renderer = structlog.dev.ConsoleRenderer(colors=True)
    
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            renderer,
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Set log level
    import logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, level.upper()),
    )
    
    # Return configured logger
    return structlog.get_logger("alloy_config_server")
