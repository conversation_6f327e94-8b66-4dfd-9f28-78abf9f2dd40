version: '3.8'

services:
  alloy-config-server:
    build:
      context: .
      dockerfile: Dockerfile.python
    ports:
      - "8080:8080"  # HTTP API
      - "8888:8888"  # gRPC
    environment:
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - USE_REDIS=false
      - ORG_NAME=default
    volumes:
      - ./conf:/app/conf:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  alloy-config-server-redis:
    build:
      context: .
      dockerfile: Dockerfile.python
    ports:
      - "8081:8080"  # HTTP API
      - "8889:8888"  # gRPC
    environment:
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - USE_REDIS=true
      - REDIS_URL=redis://redis:6379/0
      - REDIS_TTL=259200
      - ORG_NAME=default
    volumes:
      - ./conf:/app/conf:ro
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Example Alloy agent for testing
  alloy-agent:
    image: grafana/alloy:latest
    ports:
      - "12345:12345"  # Alloy HTTP port
    volumes:
      - ./example/config.alloy:/etc/alloy/config.alloy:ro
    command: ["run", "/etc/alloy/config.alloy", "--server.http.listen-addr=0.0.0.0:12345"]
    depends_on:
      - alloy-config-server

volumes:
  redis_data:
