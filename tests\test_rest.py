"""Tests for REST API."""

import pytest
from fastapi.testclient import Test<PERSON>lient
from alloy_config_server.rest import create_app


class TestRestAPI:
    """Test REST API endpoints."""
    
    @pytest.fixture
    def client(self, memory_storage, template_engine, test_settings):
        """Create test client."""
        app = create_app(memory_storage, template_engine, test_settings)
        return TestClient(app)
    
    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "storage" in data
        assert "templates" in data
    
    def test_list_templates(self, client):
        """Test listing templates."""
        response = client.get("/templates")
        assert response.status_code == 200
        
        templates = response.json()
        assert isinstance(templates, list)
        assert "default" in templates
        assert "custom" in templates
    
    @pytest.mark.asyncio
    async def test_list_configs_empty(self, client):
        """Test listing configs when empty."""
        response = client.get("/configs")
        assert response.status_code == 200
        
        configs = response.json()
        assert configs == []
    
    @pytest.mark.asyncio
    async def test_list_configs_with_data(self, client, memory_storage):
        """Test listing configs with data."""
        # Add some test data
        await memory_storage.set("test1", "config1")
        await memory_storage.set("test2", "config2")
        
        response = client.get("/configs")
        assert response.status_code == 200
        
        configs = response.json()
        assert set(configs) == {"test1", "test2"}
    
    @pytest.mark.asyncio
    async def test_get_config(self, client, memory_storage):
        """Test getting specific config."""
        # Add test data
        test_config = "prometheus.exporter.unix {}"
        await memory_storage.set("test-config", test_config)
        
        response = client.get("/configs/test-config")
        assert response.status_code == 200
        assert response.text == test_config
    
    def test_get_nonexistent_config(self, client):
        """Test getting non-existent config."""
        response = client.get("/configs/nonexistent")
        assert response.status_code == 404
    
    def test_render_template_debug_mode(self, client):
        """Test template rendering endpoint in debug mode."""
        context = {
            "Id": "test-host",
            "Attributes": {"cluster": "prod"}
        }
        
        response = client.post("/render/default", json=context)
        assert response.status_code == 200
        
        content = response.text
        assert "test-host" in content
        assert "prometheus.exporter.unix" in content
    
    def test_render_template_invalid_context(self, client):
        """Test template rendering with invalid context."""
        # Missing required fields
        context = {}
        
        response = client.post("/render/default", json=context)
        assert response.status_code == 400
