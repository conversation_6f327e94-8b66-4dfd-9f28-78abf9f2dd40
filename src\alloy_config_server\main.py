"""Main application entry point."""

import asyncio
import sys
from pathlib import Path
import uvicorn
import structlog

from .config import Settings
from .utils import setup_logging, LifecycleManager
from .storage import MemoryStorage, RedisStorage
from .templates import TemplateEngine
from .grpc import GrpcServer
from .rest import create_app

logger = structlog.get_logger(__name__)


async def create_storage(settings: Settings):
    """Create storage backend based on settings.
    
    Args:
        settings: Application settings
        
    Returns:
        Configured storage instance
    """
    if settings.use_redis and settings.redis_url:
        logger.info("Initializing Redis storage", url=settings.redis_url)
        return await RedisStorage.create(
            redis_url=settings.redis_url,
            org_name=settings.org_name,
            ttl=settings.redis_ttl
        )
    else:
        logger.info("Initializing memory storage")
        return MemoryStorage(org_name=settings.org_name)


async def main() -> None:
    """Main application function."""
    # Load settings
    settings = Settings()
    
    # Setup logging
    setup_logging(level=settings.log_level, format_type=settings.log_format)
    
    logger.info(
        "Starting Alloy Remote Config Server",
        version="0.1.0",
        grpc_port=settings.grpc_port,
        http_port=settings.http_port,
        config_folder=settings.config_folder,
        use_redis=settings.use_redis,
        org_name=settings.org_name
    )
    
    # Initialize lifecycle manager
    lifecycle = LifecycleManager()
    lifecycle.setup_signal_handlers()
    
    try:
        # Initialize storage
        storage = await create_storage(settings)
        lifecycle.add_shutdown_handler(storage.close)
        
        # Initialize template engine
        template_engine = TemplateEngine(settings.config_folder)
        await template_engine.load_templates()
        
        # Initialize gRPC server
        grpc_server = GrpcServer(
            template_engine=template_engine,
            storage=storage,
            port=settings.grpc_port,
            bind_address="0.0.0.0"
        )
        
        # Initialize REST API
        rest_app = create_app(storage, template_engine, settings)
        
        # Setup startup handlers
        lifecycle.add_startup_handler(grpc_server.start)
        
        # Setup shutdown handlers
        lifecycle.add_shutdown_handler(grpc_server.stop)
        
        # Run startup
        await lifecycle.startup()
        
        # Start HTTP server in background
        config = uvicorn.Config(
            rest_app,
            host="0.0.0.0",
            port=settings.http_port,
            log_config=None,  # Use our structured logging
            access_log=False,
        )
        http_server = uvicorn.Server(config)
        
        # Start HTTP server task
        http_task = asyncio.create_task(http_server.serve())
        lifecycle.add_shutdown_handler(lambda: http_server.shutdown())
        
        logger.info("All servers started successfully")
        
        # Wait for shutdown signal
        await lifecycle.wait_for_shutdown()
        
        logger.info("Shutdown signal received, stopping servers")
        
        # Cancel HTTP server task
        http_task.cancel()
        try:
            await http_task
        except asyncio.CancelledError:
            pass
        
        # Run shutdown handlers
        await lifecycle.shutdown()
        
        logger.info("Application shutdown complete")
        
    except Exception as e:
        logger.error("Application startup failed", error=str(e), exc_info=True)
        sys.exit(1)


def cli_main() -> None:
    """CLI entry point."""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error("Application failed", error=str(e), exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    cli_main()
