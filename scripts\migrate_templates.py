#!/usr/bin/env python3
"""Scrip<PERSON> to migrate Go templates to Jinja2 format."""

import re
import sys
from pathlib import Path
from typing import List, Tuple


def migrate_go_template_to_jinja2(content: str) -> str:
    """Convert Go template syntax to Jinja2 syntax.
    
    Args:
        content: Go template content
        
    Returns:
        Jinja2 template content
    """
    # Convert {{ .Variable }} to {{ Variable }}
    content = re.sub(r'\{\{\s*\.(\w+)\s*\}\}', r'{{ \1 }}', content)
    
    # Convert {{ .Object.Field }} to {{ Object.Field }}
    content = re.sub(r'\{\{\s*\.(\w+\.\w+(?:\.\w+)*)\s*\}\}', r'{{ \1 }}', content)
    
    # Convert range loops: {{ range .Items }} to {% for item in Items %}
    content = re.sub(
        r'\{\{\s*range\s+\.(\w+)\s*\}\}',
        r'{% for item in \1 %}',
        content
    )
    
    # Convert end range: {{ end }} to {% endfor %}
    content = re.sub(r'\{\{\s*end\s*\}\}', '{% endfor %}', content)
    
    # Convert if statements: {{ if .Condition }} to {% if Condition %}
    content = re.sub(
        r'\{\{\s*if\s+\.(\w+(?:\.\w+)*)\s*\}\}',
        r'{% if \1 %}',
        content
    )
    
    # Convert else: {{ else }} to {% else %}
    content = re.sub(r'\{\{\s*else\s*\}\}', '{% else %}', content)
    
    # Convert end if: {{ end }} to {% endif %} (this is tricky, might need manual review)
    # For now, we'll leave this as a comment for manual review
    
    return content


def migrate_template_file(input_path: Path, output_path: Path) -> bool:
    """Migrate a single template file.
    
    Args:
        input_path: Path to Go template file
        output_path: Path for Jinja2 template file
        
    Returns:
        True if migration successful, False otherwise
    """
    try:
        # Read Go template
        content = input_path.read_text(encoding='utf-8')
        
        # Convert to Jinja2
        jinja2_content = migrate_go_template_to_jinja2(content)
        
        # Add migration header
        header = f"""{{# 
Migrated from Go template: {input_path.name}
Original Go syntax converted to Jinja2
Please review for correctness, especially:
- Complex conditionals and loops
- Variable scoping
- Template functions
#}}

"""
        
        jinja2_content = header + jinja2_content
        
        # Write Jinja2 template
        output_path.write_text(jinja2_content, encoding='utf-8')
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to migrate {input_path}: {e}")
        return False


def find_go_templates(directory: Path) -> List[Path]:
    """Find all Go template files in directory.
    
    Args:
        directory: Directory to search
        
    Returns:
        List of Go template file paths
    """
    return list(directory.glob("*.conf.tmpl"))


def main():
    """Main migration function."""
    if len(sys.argv) != 3:
        print("Usage: python migrate_templates.py <input_dir> <output_dir>")
        print("Example: python migrate_templates.py conf conf_j2")
        sys.exit(1)
    
    input_dir = Path(sys.argv[1])
    output_dir = Path(sys.argv[2])
    
    if not input_dir.exists():
        print(f"❌ Input directory does not exist: {input_dir}")
        sys.exit(1)
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Find Go templates
    go_templates = find_go_templates(input_dir)
    
    if not go_templates:
        print(f"⚠️  No Go template files (*.conf.tmpl) found in {input_dir}")
        sys.exit(0)
    
    print(f"🔄 Migrating {len(go_templates)} template(s) from {input_dir} to {output_dir}")
    
    successful = 0
    failed = 0
    
    for template_path in go_templates:
        # Generate output filename: template.conf.tmpl -> template.conf.j2
        base_name = template_path.stem  # removes .tmpl
        if base_name.endswith('.conf'):
            output_name = base_name + '.j2'
        else:
            output_name = base_name + '.conf.j2'
        
        output_path = output_dir / output_name
        
        print(f"  📝 {template_path.name} -> {output_name}")
        
        if migrate_template_file(template_path, output_path):
            successful += 1
            print(f"     ✅ Migrated successfully")
        else:
            failed += 1
    
    print(f"\n📊 Migration complete:")
    print(f"   ✅ Successful: {successful}")
    print(f"   ❌ Failed: {failed}")
    
    if successful > 0:
        print(f"\n⚠️  Please review migrated templates in {output_dir}")
        print("   Pay special attention to:")
        print("   - Complex conditionals and loops")
        print("   - Variable scoping changes")
        print("   - Any custom Go template functions")
    
    if failed > 0:
        sys.exit(1)


if __name__ == "__main__":
    main()
