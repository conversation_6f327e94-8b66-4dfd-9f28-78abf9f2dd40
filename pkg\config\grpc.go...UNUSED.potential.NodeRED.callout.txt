// potential code stub to call nodered directly from the golang
// this is LESS appealling than having a replacement to alloy-remote-config-server 
// written in python as a gRPC termination - and acting as an intermediary to 
// http calls to nodered.

func (ImplementedCollectorServiceHandler) GetConfig(
	ctx context.Context,
	req *connect.Request[v1.GetConfigRequest],
) (*connect.Response[v1.GetConfigResponse], error) {
	configID := req.Msg.GetId()
 	localAttributes := req.Msg.GetLocalAttributes()
    
	// Call your NodeRED endpoint
	nodeRedURL := fmt.Sprintf("https://nodered.obs.nsw.education/alloy-config/%s", configID)
	// Add attributes as query params or POST body
    
	resp, err := http.Get(nodeRedURL)
	// ... handle response, get config content
	
	globalStorage.Set(configID, configContent)
	return connect.NewResponse(&v1.GetConfigResponse{Content: configContent}), nil
}
